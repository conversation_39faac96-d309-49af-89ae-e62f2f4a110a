{"data": [{"type": "text", "description": "Enhanced contrast analysis: See How...", "value": "4.21:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 88, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.380Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: See How...", "value": "4.21:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 91, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.383Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: See How...", "value": "4.21:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 94, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.386Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: See How...", "value": "4.21:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 97, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.389Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Improve the cost, quality, and...", "value": "1:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 98, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.389Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: See How...", "value": "4.21:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 101, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.396Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: See How...", "value": "4.21:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 104, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.399Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: See How...", "value": "4.21:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 107, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.402Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: See How...", "value": "4.21:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 110, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.405Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: PHYSICIANS...", "value": "3.01:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 123, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.430Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: IT TEAMS...", "value": "3.01:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 126, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.432Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: NURSE TEAMS...", "value": "3.01:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 129, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.435Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: EXECUTIVES...", "value": "3.01:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 132, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.439Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: RAVE Reviews by...", "value": "3.01:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 137, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.449Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: “TigerConnect Clinical Collabo...", "value": "2.78:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 139, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.450Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Director...", "value": "2.78:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 140, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.451Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: KLAS Research, October 2024...", "value": "2.78:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 141, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.451Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: “TigerConnect provides metrics...", "value": "2.78:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 142, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.451Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Director...", "value": "2.78:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 143, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.451Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: KLAS Research, September 2024...", "value": "2.78:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 144, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: “One of the reasons TigerConne...", "value": "2.78:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 145, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: CIO...", "value": "2.78:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 146, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: KLAS Research, October 2024...", "value": "2.78:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 147, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: “I am an extremely happy Tiger...", "value": "2.78:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 148, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: CIO...", "value": "2.78:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 149, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: KLAS Research, October 2024...", "value": "2.78:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 150, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: TOP RATED...", "value": "3.01:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 151, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Read Verified Reviews >...", "value": "3.01:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 155, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.457Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Read Verified Reviews >...", "value": "3.01:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 158, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.459Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Read the Report >...", "value": "3.01:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 161, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.467Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: RESOURCES...", "value": "2.26:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 163, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.469Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Explore Industry Insights & Re...", "value": "1:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 164, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.469Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: eBooks...", "value": "1:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 165, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.469Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Webinars...", "value": "1:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 166, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.469Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Infographics...", "value": "1:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 167, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.469Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Case Studies...", "value": "1:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 168, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.469Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Product Tours...", "value": "1:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 169, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.469Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Blogs...", "value": "1:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 170, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.470Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Explore All Resources...", "value": "1:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 171, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.470Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: State of Clinical Communicatio...", "value": "1:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 172, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.470Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Read the Full Report...", "value": "1:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 173, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.470Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: 2024 Gartner Magic Quadrant...", "value": "1:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 174, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.470Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Clinical Communication & Colla...", "value": "1:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 175, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.470Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Enhanced contrast analysis: Read the Full Report...", "value": "1:1 (fail) - solid background", "severity": "error", "elementCount": 1, "affectedSelectors": ["fail", "solid", "background"], "fixExample": {"before": "Current implementation", "after": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "description": "Improve color contrast to meet WCAG AA standards", "codeExample": "color: #333333; background-color: #ffffff; /* 12.6:1 ratio */", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html", "https://webaim.org/resources/contrastchecker/"]}, "metadata": {"scanDuration": 14383, "elementsAnalyzed": 272, "checkSpecificData": {"automationRate": 1, "checkType": "color-contrast-analysis", "enhancedColorAnalysis": true, "gradientDetection": false, "customPropertyResolution": false, "evidenceIndex": 176, "ruleId": "WCAG-004", "ruleName": "Contrast (Minimum)", "timestamp": "2025-07-11T10:45:01.470Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752230701661, "hash": "9f9597c7c7bf9e7fab98f005870c4503", "accessCount": 1, "lastAccessed": 1752230701661, "size": 49245}