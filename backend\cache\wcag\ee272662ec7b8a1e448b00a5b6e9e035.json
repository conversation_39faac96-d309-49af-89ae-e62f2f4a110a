{"data": [{"type": "info", "description": "Page has descriptive title", "value": "<title>#1 Healthcare Collaboration Platform | TigerConnect</title>", "selector": "title", "elementCount": 1, "affectedSelectors": ["title", "Healthcare", "Collaboration", "Platform", "TigerConnect"], "severity": "info", "metadata": {"scanDuration": 8, "elementsAnalyzed": 2, "checkSpecificData": {"automationRate": 1, "checkType": "page-title-analysis", "titleAnalysis": true, "metadataAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-013", "ruleName": "Page Titled", "timestamp": "2025-07-11T10:49:10.093Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "warning", "description": "Multiple title elements found", "value": "14 title elements detected", "selector": "title", "elementCount": 1, "affectedSelectors": ["title", "elements", "detected"], "severity": "warning", "metadata": {"scanDuration": 8, "elementsAnalyzed": 2, "checkSpecificData": {"automationRate": 1, "checkType": "page-title-analysis", "titleAnalysis": true, "metadataAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-013", "ruleName": "Page Titled", "timestamp": "2025-07-11T10:49:10.093Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752230950093, "hash": "1f881224e1020bf126ef40b6f726ff7a", "accessCount": 1, "lastAccessed": 1752230950093, "size": 1395}