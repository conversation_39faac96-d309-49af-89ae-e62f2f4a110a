{"data": [{"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Physician <PERSON>g\" -> https://schedule.tigerconnect.com/", "selector": "a:nth-of-type(6)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(6)", "Physician", "Scheduling", "https", "schedule.tigerconnect.com", "//schedule.tigerconnect.com"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.127Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: ", "value": "\"Home\" -> https://tigerconnect.com/", "selector": "a:nth-of-type(10)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(10)", "Home", "https", "tigerconnect.com", "//tigerconnect.com"], "severity": "info", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.127Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Nurses\" -> https://tigerconnect.com/healthcare-professionals/nurses/", "selector": "a:nth-of-type(19)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(19)", "Nurses", "https", "tigerconnect.com", "healthcare-professionals", "nurses", "//tigerconnect.com/healthcare-professionals/nurses"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.127Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Very short link text, Duplicate link text with different destinations", "value": "\"IT\" -> https://tigerconnect.com/healthcare-professionals/it/", "selector": "a:nth-of-type(21)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(21)", "IT", "https", "tigerconnect.com", "healthcare-professionals", "it", "//tigerconnect.com/healthcare-professionals/it"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.127Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources/", "selector": "a:nth-of-type(22)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(22)", "Resources", "https", "tigerconnect.com", "resources", "//tigerconnect.com/resources"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.127Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Physician Sc<PERSON>uling\" -> https://tigerconnect.com/products/physician-scheduling/", "selector": "a:nth-of-type(37)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(37)", "Physician", "Scheduling", "https", "tigerconnect.com", "products", "physician-scheduling", "//tigerconnect.com/products/physician-scheduling"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.127Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Clinical Collaboration\" -> https://tigerconnect.com/products/clinical-collaboration-platform/", "selector": "a:nth-of-type(38)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(38)", "Clinical", "Collaboration", "https", "tigerconnect.com", "products", "clinical-collaboration-platform", "//tigerconnect.com/products/clinical-collaboration-platform"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 6, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.127Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Alarm Management & Event Notification\" -> https://tigerconnect.com/products/alarm-management-event-notification-software/", "selector": "a:nth-of-type(39)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(39)", "Alarm", "Management", "Event", "Notification", "https", "tigerconnect.com", "products", "alarm-management-event-notification-software", "//tigerconnect.com/products/alarm-management-event-notification-software"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 7, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.127Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Patient Engagement\" -> https://tigerconnect.com/products/patient-engagement", "selector": "a:nth-of-type(40)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(40)", "Patient", "Engagement", "https", "tigerconnect.com", "products", "patient-engagement", "//tigerconnect.com/products/patient-engagement"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 8, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.127Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources/", "selector": "a:nth-of-type(43)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(43)", "Resources", "https", "tigerconnect.com", "resources", "//tigerconnect.com/resources"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 9, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.127Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources", "selector": "a:nth-of-type(57)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(57)", "Resources", "https", "tigerconnect.com", "resources", "//tigerconnect.com/resources"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 10, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.127Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: ", "value": "\"Blog\" -> https://tigerconnect.com/resources/blog-articles/", "selector": "a:nth-of-type(60)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(60)", "Blog", "https", "tigerconnect.com", "resources", "blog-articles", "//tigerconnect.com/resources/blog-articles"], "severity": "info", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 11, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.127Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Case Studies\" -> https://tigerconnect.com/resources/case-studies/", "selector": "a:nth-of-type(61)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(61)", "Case", "Studies", "https", "tigerconnect.com", "resources", "case-studies", "//tigerconnect.com/resources/case-studies"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 12, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Demo Tours\" -> https://tigerconnect.com/resources/demo-tours/", "selector": "a:nth-of-type(64)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(64)", "Demo", "Tours", "https", "tigerconnect.com", "resources", "demo-tours", "//tigerconnect.com/resources/demo-tours"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 13, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources/", "selector": "a:nth-of-type(65)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(65)", "Resources", "https", "tigerconnect.com", "resources", "//tigerconnect.com/resources"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 14, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources/", "selector": "a:nth-of-type(66)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(66)", "Resources", "https", "tigerconnect.com", "resources", "//tigerconnect.com/resources"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 15, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Webinars\" -> https://tigerconnect.com/resources/webinars/", "selector": "a:nth-of-type(74)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(74)", "Webinars", "https", "tigerconnect.com", "resources", "webinars", "//tigerconnect.com/resources/webinars"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 16, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources/", "selector": "a:nth-of-type(79)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(79)", "Resources", "https", "tigerconnect.com", "resources", "//tigerconnect.com/resources"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 17, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Case Studies\" -> https://tigerconnect.com/resources/case-studies/", "selector": "a:nth-of-type(80)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(80)", "Case", "Studies", "https", "tigerconnect.com", "resources", "case-studies", "//tigerconnect.com/resources/case-studies"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 18, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources/", "selector": "a:nth-of-type(82)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(82)", "Resources", "https", "tigerconnect.com", "resources", "//tigerconnect.com/resources"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 19, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Clinical Collaboration\" -> https://tigerconnect.com/products/physician-scheduling/", "selector": "a:nth-of-type(99)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(99)", "Clinical", "Collaboration", "https", "tigerconnect.com", "products", "physician-scheduling", "//tigerconnect.com/products/physician-scheduling"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 20, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Alarm Management & Event Notification\" -> https://tigerconnect.com/products/alarm-management-event-notification-software/", "selector": "a:nth-of-type(103)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(103)", "Alarm", "Management", "Event", "Notification", "https", "tigerconnect.com", "products", "alarm-management-event-notification-software", "//tigerconnect.com/products/alarm-management-event-notification-software"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 21, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Clinical Collaboration\" -> https://tigerconnect.com/products/physician-scheduling/", "selector": "a:nth-of-type(109)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(109)", "Clinical", "Collaboration", "https", "tigerconnect.com", "products", "physician-scheduling", "//tigerconnect.com/products/physician-scheduling"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 22, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Alarm Management & Event Notification\" -> https://tigerconnect.com/products/alarm-management-event-notification-software/", "selector": "a:nth-of-type(113)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(113)", "Alarm", "Management", "Event", "Notification", "https", "tigerconnect.com", "products", "alarm-management-event-notification-software", "//tigerconnect.com/products/alarm-management-event-notification-software"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 23, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Clinical Collaboration\" -> https://tigerconnect.com/products/physician-scheduling/", "selector": "a:nth-of-type(119)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(119)", "Clinical", "Collaboration", "https", "tigerconnect.com", "products", "physician-scheduling", "//tigerconnect.com/products/physician-scheduling"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 24, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Alarm Management & Event Notification\" -> https://tigerconnect.com/products/alarm-management-event-notification-software/", "selector": "a:nth-of-type(123)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(123)", "Alarm", "Management", "Event", "Notification", "https", "tigerconnect.com", "products", "alarm-management-event-notification-software", "//tigerconnect.com/products/alarm-management-event-notification-software"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 25, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Clinical Collaboration\" -> https://tigerconnect.com/products/physician-scheduling/", "selector": "a:nth-of-type(129)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(129)", "Clinical", "Collaboration", "https", "tigerconnect.com", "products", "physician-scheduling", "//tigerconnect.com/products/physician-scheduling"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 26, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Generic link text, Duplicate link text with different destinations", "value": "\"Learn More\" -> https://tigerconnect.com/workflows/critical-response-workflows/", "selector": "a:nth-of-type(136)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(136)", "Learn", "More", "https", "tigerconnect.com", "workflows", "critical-response-workflows", "//tigerconnect.com/workflows/critical-response-workflows"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 27, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: ", "value": "\"Play\" -> https://youtu.be/Q1bpy2rezTE", "selector": "a:nth-of-type(137)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(137)", "Play", "https", "youtu.be", "Q1bpy2rezTE", "//youtu.be/Q1bpy2rezTE"], "severity": "info", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 28, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/infographics/ed-to-inpatient-transfers-workflow-lp/", "selector": "a:nth-of-type(138)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(138)", "See", "How", "https", "tigerconnect.com", "resources", "infographics", "ed-to-inpatient-transfers-workflow-lp", "//tigerconnect.com/resources/infographics/ed-to-inpatient-transfers-workflow-lp"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 29, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/case-studies/case-study-innovation-care-partners-lp/", "selector": "a:nth-of-type(139)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(139)", "See", "How", "https", "tigerconnect.com", "resources", "case-studies", "case-study-innovation-care-partners-lp", "//tigerconnect.com/resources/case-studies/case-study-innovation-care-partners-lp"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 30, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/case-studies/baylor-st-lukes-medical-center-lp/", "selector": "a:nth-of-type(140)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(140)", "See", "How", "https", "tigerconnect.com", "resources", "case-studies", "baylor-st-lukes-medical-center-lp", "//tigerconnect.com/resources/case-studies/baylor-st-lukes-medical-center-lp"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 31, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/case-studies/video-testimonial-tufts-medical-center-lp/", "selector": "a:nth-of-type(141)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(141)", "See", "How", "https", "tigerconnect.com", "resources", "case-studies", "video-testimonial-tufts-medical-center-lp", "//tigerconnect.com/resources/case-studies/video-testimonial-tufts-medical-center-lp"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 32, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.128Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/podcasts/time-is-tissue-streamlining-clinical-communication-at-umms/", "selector": "a:nth-of-type(142)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(142)", "See", "How", "https", "tigerconnect.com", "resources", "podcasts", "time-is-tissue-streamlining-clinical-communication-at-umms", "//tigerconnect.com/resources/podcasts/time-is-tissue-streamlining-clinical-communication-at-umms"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 33, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.129Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/webinars/break-through-bottlenecks-how-sharp-memorial-is-improving-ed-workflows/", "selector": "a:nth-of-type(143)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(143)", "See", "How", "https", "tigerconnect.com", "resources", "webinars", "break-through-bottlenecks-how-sharp-memorial-is-improving-ed-workflows", "//tigerconnect.com/resources/webinars/break-through-bottlenecks-how-sharp-memorial-is-improving-ed-workflows"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 34, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.129Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/case-studies/providence-saint-johns-health-center-lp/", "selector": "a:nth-of-type(144)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(144)", "See", "How", "https", "tigerconnect.com", "resources", "case-studies", "providence-saint-johns-health-center-lp", "//tigerconnect.com/resources/case-studies/providence-saint-johns-health-center-lp"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 35, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.129Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/case-studies/case-study-trinity-health-system-lp/", "selector": "a:nth-of-type(145)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(145)", "See", "How", "https", "tigerconnect.com", "resources", "case-studies", "case-study-trinity-health-system-lp", "//tigerconnect.com/resources/case-studies/case-study-trinity-health-system-lp"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 36, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.129Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Generic link text, Duplicate link text with different destinations", "value": "\"Read More\" -> https://tigerconnect.com/healthcare-professionals/physicians/", "selector": "a:nth-of-type(151)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(151)", "Read", "More", "https", "tigerconnect.com", "healthcare-professionals", "physicians", "//tigerconnect.com/healthcare-professionals/physicians"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 37, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.129Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Generic link text, Duplicate link text with different destinations", "value": "\"Read More\" -> https://tigerconnect.com/healthcare-professionals/it/", "selector": "a:nth-of-type(152)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(152)", "Read", "More", "https", "tigerconnect.com", "healthcare-professionals", "it", "//tigerconnect.com/healthcare-professionals/it"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 38, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.129Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Link with unclear purpose: Generic link text, Duplicate link text with different destinations", "value": "\"Read More\" -> https://tigerconnect.com/healthcare-professionals/nurses/", "selector": "a:nth-of-type(153)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(153)", "Read", "More", "https", "tigerconnect.com", "healthcare-professionals", "nurses", "//tigerconnect.com/healthcare-professionals/nurses"], "severity": "error", "metadata": {"scanDuration": 72, "elementsAnalyzed": 73, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-context-analysis", "linkPurposeValidation": true, "contextualAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 39, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.129Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752230944130, "hash": "1498a8c31959998e534603e3eee908b8", "accessCount": 1, "lastAccessed": 1752230944130, "size": 38977}