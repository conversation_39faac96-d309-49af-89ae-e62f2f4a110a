{"data": [{"type": "measurement", "description": "Advanced target size analysis with optimization recommendations", "value": "{\"analysis\":[{\"element\":\"button\",\"width\":56,\"height\":41,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":56,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":1,\"height\":1,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":176.953125,\"height\":32.375,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":176.953125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":108.296875,\"height\":32.375,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":108.296875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":58.640625,\"height\":32.375,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":58.640625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":34.375,\"height\":36.375,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":160,\"height\":53.640625,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":160,\"height\":53.640625},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":67.109375,\"height\":56,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":67.109375,\"height\":56},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":124.171875,\"height\":56,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":124.171875,\"height\":56},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":88.46875,\"height\":56,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":88.46875,\"height\":56},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":93.765625,\"height\":56,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":93.765625,\"height\":56},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":157.328125,\"height\":56,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":157.328125,\"height\":56},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":146.96875,\"height\":48.796875,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":146.96875,\"height\":48.796875},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":138.0625,\"height\":43.046875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":138.0625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":122.453125,\"height\":38.546875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":122.453125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":177.875,\"height\":85,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":177.875,\"height\":85},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":197.5,\"height\":85,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":197.5,\"height\":85},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":223.5,\"height\":87,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":223.5,\"height\":87},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":169,\"height\":87,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":169,\"height\":87},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":223.5,\"height\":89,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":223.5,\"height\":89},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":205.5,\"height\":87.359375,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":205.5,\"height\":87.359375},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":223.5,\"height\":89.359375,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":223.5,\"height\":89.359375},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":166.96875,\"height\":87,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":166.96875,\"height\":87},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":223.5,\"height\":89,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":223.5,\"height\":89},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":161.765625,\"height\":84.890625,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":161.765625,\"height\":84.890625},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":222.796875,\"height\":168,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":222.796875,\"height\":168},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":222.796875,\"height\":145,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":222.796875,\"height\":145},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":222.796875,\"height\":168,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":222.796875,\"height\":168},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":222.796875,\"height\":168,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":222.796875,\"height\":168},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":222.8125,\"height\":145,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":222.8125,\"height\":145},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":148.46875,\"height\":43.171875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":148.46875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":589,\"height\":331.3125,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":589,\"height\":331.3125},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":88.359375,\"height\":34.546875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":88.359375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":88.359375,\"height\":34.546875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":88.359375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":88.359375,\"height\":34.546875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":88.359375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":88.359375,\"height\":34.546875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":88.359375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":88.359375,\"height\":34.546875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":88.359375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":88.359375,\"height\":34.546875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":88.359375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":88.359375,\"height\":34.546875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":88.359375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":88.359375,\"height\":34.546875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":88.359375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":496.796875,\"height\":96,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":496.796875,\"height\":96},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":496.796875,\"height\":96,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":496.796875,\"height\":96},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":496.796875,\"height\":69,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":496.796875,\"height\":69},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":496.796875,\"height\":138,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":496.796875,\"height\":138},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":496.796875,\"height\":96,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":496.796875,\"height\":96},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":100.359375,\"height\":34.546875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":100.359375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":100.359375,\"height\":34.546875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":100.359375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":100.359375,\"height\":34.546875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":100.359375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":100.359375,\"height\":34.546875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":100.359375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":696.390625,\"height\":191.328125,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":696.390625,\"height\":191.328125},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":696.390625,\"height\":180,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":696.390625,\"height\":180},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":696.390625,\"height\":187.5,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":696.390625,\"height\":187.5},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":262.5,\"height\":138,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":262.5,\"height\":138},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":262.5,\"height\":125.578125,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":262.5,\"height\":125.578125},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":262.5,\"height\":123.96875,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":262.5,\"height\":123.96875},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":229.21875,\"height\":128.703125,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":229.21875,\"height\":128.703125},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":231.703125,\"height\":129.265625,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":231.703125,\"height\":129.265625},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":243.34375,\"height\":128.53125,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":243.34375,\"height\":128.53125},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":176.984375,\"height\":35.578125,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":176.984375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":589,\"height\":232.96875,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":589,\"height\":232.96875},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":168.046875,\"height\":35.578125,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":168.046875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":589,\"height\":285.171875,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":589,\"height\":285.171875},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":111.5,\"height\":24,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":111.5,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":57.4375,\"height\":24,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":57.4375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":57.4375,\"height\":24,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":57.4375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":212.390625,\"height\":40,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":212.390625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":148.8125,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":148.8125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":212.390625,\"height\":42,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":212.390625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":136.359375,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":136.359375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":144.9375,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":144.9375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":95.546875,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":95.546875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":85.875,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":85.875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":212.390625,\"height\":40,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":212.390625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":166.40625,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":166.40625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":162.796875,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":162.796875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":83.890625,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":83.890625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":62.890625,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":62.890625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":85.421875,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":85.421875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":37.21875,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":212.390625,\"height\":40,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":212.390625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":137.15625,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":137.15625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":66.765625,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":66.765625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":86.203125,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":86.203125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":116.921875,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":116.921875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":85.4375,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":85.4375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":69.890625,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":69.890625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":150.296875,\"height\":40,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":150.296875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":109.9375,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":109.9375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":98.28125,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":98.28125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":95.953125,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":95.953125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":93.15625,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":93.15625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":66,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":66,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":76.53125,\"height\":22,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":76.53125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":39.328125,\"height\":19.1875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":62.65625,\"height\":19.1875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":62.65625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":27.328125,\"height\":19.1875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":36.65625,\"height\":19.1875,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":150,\"height\":46.421875,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":150,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":150,\"height\":46.421875,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":150,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":20,\"height\":28,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":20,\"height\":28,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":20,\"height\":28,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":16,\"height\":27,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":20,\"height\":28,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":127.71875,\"height\":36,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":127.71875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":139.828125,\"height\":36,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":139.828125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":139.828125,\"height\":36,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":139.828125,\"height\":48},\"touchFriendly\":false},{\"element\":\"[role=\\\"button\\\"]\",\"width\":34.375,\"height\":36.375,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"[role=\\\"button\\\"]\",\"width\":589,\"height\":331.3125,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":589,\"height\":331.3125},\"touchFriendly\":true}],\"optimization\":{\"totalTargets\":109,\"compliantTargets\":42,\"complianceRate\":38.53211009174312,\"optimizationSuggestions\":[{\"selector\":\"button\",\"currentSize\":{\"width\":56,\"height\":41},\"suggestedSize\":{\"width\":56,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 56x41px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":1,\"height\":1},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 1x1px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":176.953125,\"height\":32.375},\"suggestedSize\":{\"width\":176.953125,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 176.953125x32.375px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":108.296875,\"height\":32.375},\"suggestedSize\":{\"width\":108.296875,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 108.296875x32.375px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":58.640625,\"height\":32.375},\"suggestedSize\":{\"width\":58.640625,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 58.640625x32.375px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":34.375,\"height\":36.375},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 34.375x36.375px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":138.0625,\"height\":43.046875},\"suggestedSize\":{\"width\":138.0625,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 138.0625x43.046875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":122.453125,\"height\":38.546875},\"suggestedSize\":{\"width\":122.453125,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 122.453125x38.546875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":148.46875,\"height\":43.171875},\"suggestedSize\":{\"width\":148.46875,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 148.46875x43.171875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":88.359375,\"height\":34.546875},\"suggestedSize\":{\"width\":88.359375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 88.359375x34.546875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":88.359375,\"height\":34.546875},\"suggestedSize\":{\"width\":88.359375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 88.359375x34.546875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":88.359375,\"height\":34.546875},\"suggestedSize\":{\"width\":88.359375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 88.359375x34.546875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":88.359375,\"height\":34.546875},\"suggestedSize\":{\"width\":88.359375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 88.359375x34.546875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":88.359375,\"height\":34.546875},\"suggestedSize\":{\"width\":88.359375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 88.359375x34.546875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":88.359375,\"height\":34.546875},\"suggestedSize\":{\"width\":88.359375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 88.359375x34.546875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":88.359375,\"height\":34.546875},\"suggestedSize\":{\"width\":88.359375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 88.359375x34.546875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":88.359375,\"height\":34.546875},\"suggestedSize\":{\"width\":88.359375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 88.359375x34.546875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":100.359375,\"height\":34.546875},\"suggestedSize\":{\"width\":100.359375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 100.359375x34.546875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":100.359375,\"height\":34.546875},\"suggestedSize\":{\"width\":100.359375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 100.359375x34.546875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":100.359375,\"height\":34.546875},\"suggestedSize\":{\"width\":100.359375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 100.359375x34.546875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":100.359375,\"height\":34.546875},\"suggestedSize\":{\"width\":100.359375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 100.359375x34.546875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":176.984375,\"height\":35.578125},\"suggestedSize\":{\"width\":176.984375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 176.984375x35.578125px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":168.046875,\"height\":35.578125},\"suggestedSize\":{\"width\":168.046875,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 168.046875x35.578125px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":111.5,\"height\":24},\"suggestedSize\":{\"width\":111.5,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 111.5x24px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":57.4375,\"height\":24},\"suggestedSize\":{\"width\":57.4375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 57.4375x24px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":57.4375,\"height\":24},\"suggestedSize\":{\"width\":57.4375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 57.4375x24px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":212.390625,\"height\":40},\"suggestedSize\":{\"width\":212.390625,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 212.390625x40px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":148.8125,\"height\":22},\"suggestedSize\":{\"width\":148.8125,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 148.8125x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":212.390625,\"height\":42},\"suggestedSize\":{\"width\":212.390625,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 212.390625x42px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":136.359375,\"height\":22},\"suggestedSize\":{\"width\":136.359375,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 136.359375x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":144.9375,\"height\":22},\"suggestedSize\":{\"width\":144.9375,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 144.9375x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":95.546875,\"height\":22},\"suggestedSize\":{\"width\":95.546875,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 95.546875x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":85.875,\"height\":22},\"suggestedSize\":{\"width\":85.875,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 85.875x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":212.390625,\"height\":40},\"suggestedSize\":{\"width\":212.390625,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 212.390625x40px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":166.40625,\"height\":22},\"suggestedSize\":{\"width\":166.40625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 166.40625x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":162.796875,\"height\":22},\"suggestedSize\":{\"width\":162.796875,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 162.796875x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":83.890625,\"height\":22},\"suggestedSize\":{\"width\":83.890625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 83.890625x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":62.890625,\"height\":22},\"suggestedSize\":{\"width\":62.890625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 62.890625x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":85.421875,\"height\":22},\"suggestedSize\":{\"width\":85.421875,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 85.421875x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":37.21875,\"height\":22},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 37.21875x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":212.390625,\"height\":40},\"suggestedSize\":{\"width\":212.390625,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 212.390625x40px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":137.15625,\"height\":22},\"suggestedSize\":{\"width\":137.15625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 137.15625x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":66.765625,\"height\":22},\"suggestedSize\":{\"width\":66.765625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 66.765625x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":86.203125,\"height\":22},\"suggestedSize\":{\"width\":86.203125,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 86.203125x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":116.921875,\"height\":22},\"suggestedSize\":{\"width\":116.921875,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 116.921875x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":85.4375,\"height\":22},\"suggestedSize\":{\"width\":85.4375,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 85.4375x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":69.890625,\"height\":22},\"suggestedSize\":{\"width\":69.890625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 69.890625x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":150.296875,\"height\":40},\"suggestedSize\":{\"width\":150.296875,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 150.296875x40px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":109.9375,\"height\":22},\"suggestedSize\":{\"width\":109.9375,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 109.9375x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":98.28125,\"height\":22},\"suggestedSize\":{\"width\":98.28125,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 98.28125x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":95.953125,\"height\":22},\"suggestedSize\":{\"width\":95.953125,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 95.953125x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":93.15625,\"height\":22},\"suggestedSize\":{\"width\":93.15625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 93.15625x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":66,\"height\":22},\"suggestedSize\":{\"width\":66,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 66x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":76.53125,\"height\":22},\"suggestedSize\":{\"width\":76.53125,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 76.53125x22px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":39.328125,\"height\":19.1875},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 39.328125x19.1875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":62.65625,\"height\":19.1875},\"suggestedSize\":{\"width\":62.65625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 62.65625x19.1875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":27.328125,\"height\":19.1875},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 27.328125x19.1875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":36.65625,\"height\":19.1875},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 36.65625x19.1875px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":20,\"height\":28},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 20x28px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":20,\"height\":28},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 20x28px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":20,\"height\":28},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 20x28px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":16,\"height\":27},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 16x27px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":20,\"height\":28},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 20x28px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":127.71875,\"height\":36},\"suggestedSize\":{\"width\":127.71875,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 127.71875x36px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":139.828125,\"height\":36},\"suggestedSize\":{\"width\":139.828125,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 139.828125x36px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":139.828125,\"height\":36},\"suggestedSize\":{\"width\":139.828125,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 139.828125x36px is below WCAG minimum of 44x44px\"},{\"selector\":\"[role=\\\"button\\\"]\",\"currentSize\":{\"width\":34.375,\"height\":36.375},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 34.375x36.375px is below WCAG minimum of 44x44px\"}]},\"thirdPartyEnhanced\":false}", "severity": "error", "elementCount": 1, "affectedSelectors": ["analysis", "element", "button", "width", "height", "meetsMinimum", "false", "recommendedSize", "touchFriendly", "a[href]", "true", "role", "optimization", "totalTargets", "compliantTargets", "complianceRate", "optimizationSuggestions", "selector", "currentSize", "suggestedSize", "priority", "medium", "reason", "Current", "size", "x41px", "is", "below", "WCAG", "minimum", "of", "x44px", "high", "x1px", "x32", "px", "x36", "x43", "x38", "x34", "x35", "x24px", "x40px", "x22px", "x42px", "x19", "x28px", "x27px", "x36px", "thirdPartyEnhanced"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.174Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 1x1px (adequate spacing)", "selector": "a.skip-link.screen-reader-text.scroll-ignore", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.skip-link.screen-reader-text.scroll-ignore", "Size", "x1px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.174Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target fails size requirements", "value": "Size: 177x32px (minimum: 44x44px)", "selector": "a:nth-of-type(2)", "severity": "error", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(2)", "Size", "x32px", "minimum", "x44px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.174Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target fails size requirements", "value": "Size: 108x32px (minimum: 44x44px)", "selector": "a:nth-of-type(3)", "severity": "error", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(3)", "Size", "x32px", "minimum", "x44px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.174Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target fails size requirements", "value": "Size: 59x32px (minimum: 44x44px)", "selector": "a:nth-of-type(4)", "severity": "error", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(4)", "Size", "x32px", "minimum", "x44px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.174Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target fails size requirements", "value": "Size: 34x36px (minimum: 44x44px)", "selector": "a:nth-of-type(8)", "severity": "error", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(8)", "Size", "x36px", "minimum", "x44px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 6, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.174Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 160x54px (adequate spacing)", "selector": "a.brand.has-logo-image", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.brand.has-logo-image", "Size", "x54px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 7, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.174Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 67x56px ", "selector": "a:nth-of-type(10)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(10)", "Size", "x56px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 8, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.174Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 124x56px ", "selector": "a:nth-of-type(11)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(11)", "Size", "x56px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 9, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.174Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 88x56px ", "selector": "a:nth-of-type(32)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(32)", "Size", "x56px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 10, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.174Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 94x56px ", "selector": "a:nth-of-type(51)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(51)", "Size", "x56px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 11, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.174Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 157x56px ", "selector": "a:nth-of-type(76)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(76)", "Size", "x56px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 12, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.175Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 147x49px (adequate spacing)", "selector": "a.button.header-button.button-size-custom.button-style-filled", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.button.header-button.button-size-custom.button-style-filled", "Size", "x49px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 13, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.175Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 138x43px (adequate spacing)", "selector": "a.kb-button.kt-button.button.kb-btn15335_361967-05.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-button.kt-button.button.kb-btn15335_361967-05.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "Size", "x43px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 14, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.175Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 122x39px (adequate spacing)", "selector": "a.kb-button.kt-button.button.kb-btn15335_099863-04.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-button.kt-button.button.kb-btn15335_099863-04.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "Size", "x39px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 15, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.175Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 178x85px (adequate spacing)", "selector": "a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "Size", "x85px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 16, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.175Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 198x85px ", "selector": "a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "Size", "x85px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 17, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.175Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 224x87px ", "selector": "a.kb-section-link-overlay", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-section-link-overlay", "Size", "x87px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 18, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.175Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 169x87px ", "selector": "a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "Size", "x87px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 19, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.175Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 224x89px ", "selector": "a.kb-section-link-overlay", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-section-link-overlay", "Size", "x89px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 20, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.175Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 206x87px ", "selector": "a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "Size", "x87px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 21, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.175Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 224x89px ", "selector": "a.kb-section-link-overlay", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-section-link-overlay", "Size", "x89px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 22, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.175Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 167x87px ", "selector": "a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "Size", "x87px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 23, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.175Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 224x89px ", "selector": "a.kb-section-link-overlay", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-section-link-overlay", "Size", "x89px"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 24, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.175Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 162x85px (adequate spacing)", "selector": "a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "Size", "x85px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 25, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.175Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 223x168px (adequate spacing)", "selector": "a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center", "Size", "x168px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 26, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.176Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 223x145px (adequate spacing)", "selector": "a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center", "Size", "x145px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 27, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.176Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 223x168px (adequate spacing)", "selector": "a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center", "Size", "x168px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 28, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.176Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 223x168px (adequate spacing)", "selector": "a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center", "Size", "x168px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 29, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.176Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 223x145px (adequate spacing)", "selector": "a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center", "Size", "x145px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 30, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.176Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 148x43px (adequate spacing)", "selector": "a.kb-button.kt-button.button.kb-btn15335_dfd3b6-71.kt-btn-size-standard.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-button.kt-button.button.kb-btn15335_dfd3b6-71.kt-btn-size-standard.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "Size", "x43px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 31, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.176Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 589x331px (adequate spacing)", "selector": "a.kadence-video-popup-link.kadence-video-type-external", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kadence-video-popup-link.kadence-video-type-external", "Size", "x331px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 32, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.176Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 88x35px (adequate spacing)", "selector": "a.kb-button.kt-button.button.kb-btn15335_ade17d-c4.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-button.kt-button.button.kb-btn15335_ade17d-c4.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "Size", "x35px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 33, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.176Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 88x35px (adequate spacing)", "selector": "a.kb-button.kt-button.button.kb-btn15335_affa7a-67.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-button.kt-button.button.kb-btn15335_affa7a-67.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "Size", "x35px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 34, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.176Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 88x35px (adequate spacing)", "selector": "a.kb-button.kt-button.button.kb-btn15335_ff539f-b1.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-button.kt-button.button.kb-btn15335_ff539f-b1.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "Size", "x35px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 35, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.176Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 88x35px (adequate spacing)", "selector": "a.kb-button.kt-button.button.kb-btn15335_3e7e82-8d.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-button.kt-button.button.kb-btn15335_3e7e82-8d.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "Size", "x35px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 36, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.176Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 88x35px (adequate spacing)", "selector": "a.kb-button.kt-button.button.kb-btn15335_981f0f-b1.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-button.kt-button.button.kb-btn15335_981f0f-b1.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "Size", "x35px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 37, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.176Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 88x35px (adequate spacing)", "selector": "a.kb-button.kt-button.button.kb-btn15335_637b91-f5.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-button.kt-button.button.kb-btn15335_637b91-f5.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "Size", "x35px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 38, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.176Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 88x35px (adequate spacing)", "selector": "a.kb-button.kt-button.button.kb-btn15335_4b75e1-85.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-button.kt-button.button.kb-btn15335_4b75e1-85.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "Size", "x35px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 39, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.177Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 88x35px (adequate spacing)", "selector": "a.kb-button.kt-button.button.kb-btn15335_965685-cb.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-button.kt-button.button.kb-btn15335_965685-cb.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn", "Size", "x35px", "adequate", "spacing"], "metadata": {"scanDuration": 403, "elementsAnalyzed": 107, "checkSpecificData": {"automationRate": 1, "checkType": "target-size-analysis", "dimensionMeasurement": true, "interactiveElementAnalysis": true, "evidenceIndex": 40, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T10:35:04.177Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752230104177, "hash": "5777011227dd476f4da838d4a11c3029", "accessCount": 1, "lastAccessed": 1752230104177, "size": 68607}