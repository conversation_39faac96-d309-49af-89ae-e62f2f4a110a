{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Cannot read properties of undefined (reading 'getElementSelector')\npptr:evaluate;FormAccessibilityAnalyzer.getForms%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cutils%5Cform-accessibility-analyzer.ts%3A434%3A27):7:44", "severity": "error", "elementCount": 1, "affectedSelectors": ["Cannot", "read", "properties", "of", "undefined", "reading", "getElementSelector", "pptr", "evaluate", "FormAccessibilityAnalyzer.getForms", "D", "A", "CWeb", "projects", "CCom<PERSON>ly", "Checker", "Cbackend", "Csrc", "Ccompliance", "Cwcag", "Cutils", "Cform-accessibility-analyzer.ts", "A434", "A27"], "fixExample": {"before": "Current implementation", "after": "<h1>Main Title</h1><h2>Section</h2><h3>Subsection</h3>", "description": "Add proper heading structure", "codeExample": "<h1>Main Title</h1><h2>Section</h2><h3>Subsection</h3>", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/headings-and-labels.html", "https://webaim.org/techniques/semanticstructure/"]}, "metadata": {"scanDuration": 1433, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 1, "checkType": "focus-visibility-analysis", "focusTracking": true, "visualIndicators": true, "evidenceIndex": 0, "ruleId": "WCAG-007", "ruleName": "Focus Visible", "timestamp": "2025-07-11T10:45:13.717Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752230713717, "hash": "c7837ce731e332ada9888499595f82f6", "accessCount": 1, "lastAccessed": 1752230713717, "size": 1501}