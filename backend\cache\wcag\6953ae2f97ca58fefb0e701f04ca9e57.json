{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Cannot read properties of undefined (reading 'getElementSelector')\npptr:evaluate;FormAccessibilityAnalyzer.getForms%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cutils%5Cform-accessibility-analyzer.ts%3A434%3A27):7:44", "severity": "error", "elementCount": 1, "affectedSelectors": ["Cannot", "read", "properties", "of", "undefined", "reading", "getElementSelector", "pptr", "evaluate", "FormAccessibilityAnalyzer.getForms", "D", "A", "CWeb", "projects", "CCom<PERSON>ly", "Checker", "Cbackend", "Csrc", "Ccompliance", "Cwcag", "Cutils", "Cform-accessibility-analyzer.ts", "A434", "A27"], "metadata": {"scanDuration": 13, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-11T10:46:27.558Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752230787558, "hash": "8a54a5d93ace3e27d3c5974409b3965b", "accessCount": 1, "lastAccessed": 1752230787558, "size": 1218}