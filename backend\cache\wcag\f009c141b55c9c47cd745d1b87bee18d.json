{"data": [{"type": "text", "description": "Unusual words without definitions", "value": "Found 13 unusual words that may need definitions for AAA compliance", "elementCount": 1, "affectedSelectors": ["Found", "unusual", "words", "that", "may", "need", "definitions", "for", "AAA", "compliance"], "severity": "warning", "metadata": {"scanDuration": 161, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T10:46:33.058Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Unusual word without definition: \"ems\"", "value": "Context: \"flex-direction:column;align-items:flex-start;}.kadence-column41\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 161, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T10:46:33.059Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "flex-direction", "column", "align-items", "flex-start", ".kadence-column41"]}, {"type": "text", "description": "Unusual word without definition: \"physicians\"", "value": "Context: \"bottom:0px;padding-left:0px;}\nPhysicians\n\n\n.kt-svg-icon-list-item-419_\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 161, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T10:46:33.059Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "bottom", "px", "padding-left", "Physicians", ".kt-svg-icon-list-item-419_"]}, {"type": "text", "description": "Unusual word without definition: \"teams\"", "value": "Context: \":0px;}\nCoordinate care across teams and facilities with a full su\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 161, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T10:46:33.059Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "px", "Coordinate", "care", "across", "teams", "and", "facilities", "with", "a", "full", "su"]}, {"type": "text", "description": "Unusual word without definition: \"nurse\"", "value": "Context: \"bottom:0px;padding-left:0px;}\nNurses\n\n\n.kt-svg-icon-list-item-419\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 161, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T10:46:33.059Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "bottom", "px", "padding-left", "Nurses", ".kt-svg-icon-list-item-419"]}, {"type": "text", "description": "Unusual word without definition: \"executives\"", "value": "Context: \"bottom:0px;padding-left:0px;}\nExecutives\n\n\n.kt-svg-icon-list-item-419_\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 161, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T10:46:33.059Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "bottom", "px", "padding-left", "Executives", ".kt-svg-icon-list-item-419_"]}, {"type": "text", "description": "Unusual word without definition: \"rave\"", "value": "Context: \"ealthcare Communication\n\n\n\n\n\n\nRAVE Reviews by\n\n\n\nReal Customers\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 161, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 6, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T10:46:33.059Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "ealthcare", "Communication", "RAVE", "Reviews", "by", "Real", "Customers"]}, {"type": "text", "description": "Unusual word without definition: \"klas\"", "value": "Context: \"ement for us.”\n\n\n\nDirector\n\n\n\nKLAS Research, October 2024\n\n\n\n\n\n“\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 161, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 7, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T10:46:33.059Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "ement", "for", "us", "Director", "KLAS", "Research", "October"]}, {"type": "text", "description": "Unusual word without definition: \"ehr\"", "value": "Context: \"we hope to integrate with our EHR so that we can send messages\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 161, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 8, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T10:46:33.059Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "we", "hope", "to", "integrate", "with", "our", "EHR", "so", "that", "can", "send", "messages"]}, {"type": "text", "description": "Unusual word without definition: \"cio\"", "value": "Context: \"ed rather than bolted on.”\n\n\n\nCIO\n\n\n\nKLAS Research, October 202\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 161, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 9, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T10:46:33.059Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "ed", "rather", "than", "bolted", "on", "CIO", "KLAS", "Research", "October"]}, {"type": "text", "description": "Unusual word without definition: \"top\"", "value": "Context: \"content-edge-padding);padding-top:var(--global-kb-spacing-md, 2\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 161, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 10, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T10:46:33.059Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "content-edge-padding", "padding-top", "var", "global-kb-spacing-md"]}], "timestamp": 1752230793059, "hash": "c46ff3e419f38c4b082b53681633caff", "accessCount": 1, "lastAccessed": 1752230793059, "size": 9150}