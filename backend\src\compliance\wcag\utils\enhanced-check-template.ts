/**
 * Enhanced Check Template with Axe-core Integration
 * Extends the existing CheckTemplate to include axe-core validation while maintaining backward compatibility
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig, CheckFunction } from './check-template';
import { WcagCheckResult } from '../types';

import { EvidenceStandardizer } from './evidence-standardizer';
import SmartCache from './smart-cache';
import logger from '../../../utils/logger';
import UtilityIntegrationManager, { UtilityIntegrationConfig } from './utility-integration-manager';

// Axe-core integration with fallback
interface AxeCoreLib {
  source: string;
  run: (
    context?: unknown,
    options?: unknown,
  ) => Promise<{
    violations: Array<{
      id: string;
      impact: string;
      description: string;
      nodes: Array<{ target: string[]; html: string }>;
    }>;
    passes: Array<{
      id: string;
      impact: string;
      description: string;
      nodes: Array<{ target: string[]; html: string }>;
    }>;
    incomplete: Array<{
      id: string;
      impact: string;
      description: string;
      nodes: Array<{ target: string[]; html: string }>;
    }>;
    inapplicable: Array<{
      id: string;
      impact: string;
      description: string;
      nodes: Array<{ target: string[]; html: string }>;
    }>;
  }>;
}

let axeCore: AxeCoreLib | null = null;
try {
  axeCore = require('axe-core');
} catch (error) {
  logger.debug('axe-core library not available, enhanced validation disabled');
}

export interface EnhancedCheckConfig extends CheckConfig {
  // Enhanced validation options
  enableAxeValidation?: boolean;
  axeRules?: string[];
  enablePerformanceMetrics?: boolean;
  enableDetailedEvidence?: boolean;
  // Utility integration options
  utilityConfig?: Partial<UtilityIntegrationConfig>;
  enableUtilityIntegration?: boolean;
}

export interface AxeValidationResult {
  violations: Array<{
    id: string;
    impact: string;
    description: string;
    helpUrl?: string;
    nodes: Array<{ target: string[]; html: string }>;
  }>;
  passes: Array<{
    id: string;
    impact: string;
    description: string;
    helpUrl?: string;
    nodes: Array<{ target: string[]; html: string }>;
  }>;
  incomplete: Array<{
    id: string;
    impact: string;
    description: string;
    helpUrl?: string;
    nodes: Array<{ target: string[]; html: string }>;
  }>;
  inapplicable: Array<{
    id: string;
    impact: string;
    description: string;
    helpUrl?: string;
    nodes: Array<{ target: string[]; html: string }>;
  }>;
  executionTime: number;
  rulesExecuted: string[];
}

export class EnhancedCheckTemplate extends CheckTemplate {
  private utilityManager: UtilityIntegrationManager;
  private smartCache: SmartCache;

  constructor() {
    super();
    this.utilityManager = UtilityIntegrationManager.getInstance();
    this.smartCache = SmartCache.getInstance();
  }

  /**
   * Execute a WCAG check with enhanced validation and utility integration
   */
  async executeEnhancedCheck<T extends EnhancedCheckConfig>(
    ruleId: string,
    ruleName: string,
    category: string,
    weight: number,
    level: string,
    config: T,
    checkFunction: CheckFunction<T>,
    requiresBrowser: boolean = true,
    requiresManualReview: boolean = false,
    enhancementOptions?: {
      enableAxeValidation?: boolean;
      axeRules?: string[];
      mergeStrategy?: 'supplement' | 'validate' | 'enhance';
    },
  ): Promise<WcagCheckResult> {
    const startTime = Date.now();

    try {
      logger.info(`🔍 [${config.scanId}] Starting enhanced ${ruleId}: ${ruleName}`);

      // Generate optimized content hash for caching
      const pageContent = await config.page?.content() || '';
      const contentHash = this.smartCache.generateOptimizedContentHash(pageContent);
      const configHash = require('crypto').createHash('md5').update(JSON.stringify({
        ruleId,
        requiresBrowser,
        requiresManualReview,
        targetUrl: config.targetUrl
      })).digest('hex');

      // Check cache first
      const cachedResult = await this.smartCache.getRuleResult<WcagCheckResult>(
        ruleId,
        contentHash,
        configHash
      );

      if (cachedResult) {
        logger.info(`🎯 [${config.scanId}] Cache hit for ${ruleId}`);
        return cachedResult;
      }

      logger.debug(`🔍 [${config.scanId}] Cache miss for ${ruleId}, executing check`);
      logger.debug(`🔍 [${config.scanId}] Cache key: rule:${ruleId}:${contentHash.substring(0, 8)}:${configHash.substring(0, 8)}`);

      // Handle manual review checks differently
      let baseResult: WcagCheckResult;

      if (requiresManualReview) {
        // For manual review checks, execute directly without base template restrictions
        logger.info(`🔍 [${config.scanId}] Executing manual review check ${ruleId}`);

        try {
          // Ensure page is still valid before executing check
          await this.validatePageState(config.page!);

          const checkResult = await checkFunction(config.page!, config);

          // Convert manual review result to standard WcagCheckResult format
          const manualResult = checkResult as {
            automatedScore?: number;
            maxScore: number;
            evidence: import('../types').WcagEvidence[];
            issues?: string[];
            recommendations: string[];
            manualReviewItems?: import('../types').WcagManualReviewItem[];
          };

          const automatedScore = manualResult.automatedScore || 0;
          baseResult = {
            ruleId,
            ruleName,
            category: category as import('../types').WcagCategory,
            wcagVersion: '2.2' as import('../types').WcagVersion,
            successCriterion: '0.0.0', // Will be set by orchestrator
            level: level as import('../types').WcagLevel,
            status: automatedScore >= manualResult.maxScore * 0.8 ? 'passed' : 'failed',
            score: automatedScore,
            maxScore: manualResult.maxScore,
            weight: weight,
            automated: false, // Manual review checks are not fully automated
            evidence: manualResult.evidence,
            recommendations: manualResult.recommendations,
            executionTime: 0,
            errorMessage: manualResult.issues?.join('; '),
            manualReviewItems: manualResult.manualReviewItems || [],
          };
        } catch (error) {
          logger.error(`❌ Manual review check ${ruleId} failed:`, { error });
          baseResult = {
            ruleId,
            ruleName,
            category: category as import('../types').WcagCategory,
            wcagVersion: '2.2' as import('../types').WcagVersion,
            successCriterion: '0.0.0',
            level: level as import('../types').WcagLevel,
            status: 'failed',
            score: 0,
            maxScore: 100,
            weight: weight,
            automated: false,
            evidence: [],
            recommendations: ['Manual review required'],
            executionTime: 0,
            errorMessage: `Manual review check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            manualReviewItems: [],
          };
        }
      } else {
        // Use base CheckTemplate for fully automated checks
        baseResult = await this.executeCheck(
          ruleId,
          ruleName,
          category,
          weight,
          level,
          config,
          checkFunction,
          requiresBrowser,
          requiresManualReview,
        );
      }

      let enhancedResult = baseResult;

      // Apply utility integration if enabled
      if (config.enableUtilityIntegration && config.page) {
        // Ensure page is still valid before applying utility enhancements
        await this.validatePageState(config.page);

        enhancedResult = await this.applyUtilityEnhancements(
          baseResult,
          config.page,
          ruleId,
          config.scanId,
          config.utilityConfig,
        );
      }

      // Apply axe-core enhancements if enabled and available
      if (enhancementOptions?.enableAxeValidation && axeCore && config.page) {
        const axeResult = await this.runAxeValidation(
          config.page,
          ruleId,
          enhancementOptions.axeRules,
        );

        // Merge results based on strategy
        const mergedResult = this.mergeWithAxeResults(
          enhancedResult,
          axeResult,
          enhancementOptions.mergeStrategy || 'supplement',
        );

        // Standardize evidence format
        const enhancedEvidence = EvidenceStandardizer.standardizeEvidence(mergedResult.evidence, {
          ruleId,
          ruleName,
          scanDuration: Date.now() - startTime,
          elementsAnalyzed: axeResult.violations.length + axeResult.passes.length,
          checkSpecificData: {
            axeValidationEnabled: true,
            axeRulesExecuted: axeResult.rulesExecuted,
            axeExecutionTime: axeResult.executionTime,
            mergeStrategy: enhancementOptions.mergeStrategy,
            enhancedAccuracy: true,
          },
        });

        const finalResult = {
          ...mergedResult,
          evidence: enhancedEvidence,
        };

        // Cache the result
        await this.smartCache.cacheRuleResult(ruleId, contentHash, finalResult, configHash);
        logger.debug(`💾 [${config.scanId}] Cached result for ${ruleId}`);

        return finalResult;
      }

      // Return base result with standardized evidence if no enhancement
      const enhancedEvidence = EvidenceStandardizer.standardizeEvidence(baseResult.evidence, {
        ruleId,
        ruleName,
        scanDuration: Date.now() - startTime,
        elementsAnalyzed: 0,
        checkSpecificData: {
          axeValidationEnabled: false,
          enhancedAccuracy: false,
        },
      });

      const finalResult = {
        ...baseResult,
        evidence: enhancedEvidence,
      };

      // Cache the result
      await this.smartCache.cacheRuleResult(ruleId, contentHash, finalResult, configHash);
      logger.debug(`💾 [${config.scanId}] Cached result for ${ruleId}`);

      return finalResult;
    } catch (error) {
      logger.error(`❌ [${config.scanId}] Enhanced check ${ruleId} failed:`, {
        error: error instanceof Error ? error.message : String(error),
      });

      // Fallback to base implementation on error
      return this.executeCheck(
        ruleId,
        ruleName,
        category,
        weight,
        level,
        config,
        checkFunction,
        requiresBrowser,
        requiresManualReview,
      );
    }
  }

  /**
   * Run axe-core validation for specific WCAG rules
   */
  private async runAxeValidation(
    page: Page,
    wcagRuleId: string,
    customRules?: string[],
  ): Promise<AxeValidationResult> {
    const axeStartTime = Date.now();

    try {
      // Map WCAG rule IDs to axe-core rules
      const axeRuleMap = this.getAxeRuleMapping();
      const relevantRules = customRules || axeRuleMap[wcagRuleId] || [];

      if (relevantRules.length === 0) {
        logger.debug(`No axe-core rules mapped for ${wcagRuleId}`);
        return {
          violations: [],
          passes: [],
          incomplete: [],
          inapplicable: [],
          executionTime: 0,
          rulesExecuted: [],
        };
      }

      // Inject axe-core into the page
      if (!axeCore) {
        throw new Error('axe-core is not available');
      }
      await page.addScriptTag({
        content: axeCore.source,
      });

      // Run axe-core with specific rules
      const axeResults = (await page.evaluate((rules) => {
        return (
          window as unknown as Window & {
            axe: {
              run: (
                context: Document,
                options: {
                  rules: Record<string, { enabled: boolean }>;
                  resultTypes: string[];
                },
              ) => Promise<unknown>;
            };
          }
        ).axe.run(document, {
          rules: rules.reduce((acc: Record<string, { enabled: boolean }>, rule: string) => {
            acc[rule] = { enabled: true };
            return acc;
          }, {}),
          resultTypes: ['violations', 'passes', 'incomplete', 'inapplicable'],
        });
      }, relevantRules)) as {
        violations: Array<{
          id: string;
          impact: string;
          description: string;
          helpUrl?: string;
          nodes: Array<{ target: string[]; html: string }>;
        }>;
        passes: Array<{
          id: string;
          impact: string;
          description: string;
          helpUrl?: string;
          nodes: Array<{ target: string[]; html: string }>;
        }>;
        incomplete: Array<{
          id: string;
          impact: string;
          description: string;
          helpUrl?: string;
          nodes: Array<{ target: string[]; html: string }>;
        }>;
        inapplicable: Array<{
          id: string;
          impact: string;
          description: string;
          helpUrl?: string;
          nodes: Array<{ target: string[]; html: string }>;
        }>;
      };

      const executionTime = Date.now() - axeStartTime;

      logger.debug(`Axe-core validation completed for ${wcagRuleId}`, {
        violations: axeResults.violations.length,
        passes: axeResults.passes.length,
        executionTime,
      });

      return {
        violations: axeResults.violations || [],
        passes: axeResults.passes || [],
        incomplete: axeResults.incomplete || [],
        inapplicable: axeResults.inapplicable || [],
        executionTime,
        rulesExecuted: relevantRules,
      };
    } catch (error) {
      logger.warn(`Axe-core validation failed for ${wcagRuleId}:`, {
        error: error instanceof Error ? error.message : String(error),
      });
      return {
        violations: [],
        passes: [],
        incomplete: [],
        inapplicable: [],
        executionTime: Date.now() - axeStartTime,
        rulesExecuted: [],
      };
    }
  }

  /**
   * Merge base check results with axe-core validation results
   */
  private mergeWithAxeResults(
    baseResult: WcagCheckResult,
    axeResult: AxeValidationResult,
    strategy: 'supplement' | 'validate' | 'enhance',
  ): WcagCheckResult {
    const mergedEvidence = [...baseResult.evidence];
    const mergedRecommendations = [...(baseResult.recommendations || [])];

    // Add axe-core validation summary
    mergedEvidence.push({
      type: 'info',
      description: 'Axe-core validation summary',
      value: `Violations: ${axeResult.violations.length}, Passes: ${axeResult.passes.length}, Incomplete: ${axeResult.incomplete.length}`,
      severity: 'info',
    });

    // Process violations
    axeResult.violations.forEach((violation) => {
      mergedEvidence.push({
        type: 'code',
        description: `Axe-core violation: ${violation.description}`,
        value: `${violation.id} - Impact: ${violation.impact}`,
        selector: violation.nodes?.[0]?.target?.join(', ') || '',
        severity: this.mapAxeImpactToSeverity(violation.impact),
      });

      mergedRecommendations.push(`Issue: ${violation.description}`);

      if (violation.helpUrl) {
        mergedRecommendations.push(`See: ${violation.helpUrl}`);
      }
    });

    // Process incomplete checks (require manual review)
    axeResult.incomplete.forEach((incomplete) => {
      mergedEvidence.push({
        type: 'interaction',
        description: `Axe-core incomplete check: ${incomplete.description}`,
        value: `${incomplete.id} - Requires manual verification`,
        selector: incomplete.nodes?.[0]?.target?.join(', ') || '',
        severity: 'warning',
      });
    });

    // Adjust score based on strategy and axe results
    let adjustedScore = baseResult.score;

    if (strategy === 'validate' && axeResult.violations.length > 0) {
      // If axe-core finds violations, reduce confidence in base result
      adjustedScore = Math.min(adjustedScore, 50);
    } else if (strategy === 'enhance' && axeResult.passes.length > 0) {
      // If axe-core confirms passes, boost confidence
      adjustedScore = Math.min(adjustedScore + 10, 100);
    }

    return {
      ...baseResult,
      score: adjustedScore,
      evidence: mergedEvidence,
      recommendations: mergedRecommendations,
    };
  }

  /**
   * Map WCAG rule IDs to axe-core rule IDs
   */
  private getAxeRuleMapping(): Record<string, string[]> {
    return {
      'WCAG-001': ['image-alt', 'image-redundant-alt', 'input-image-alt'],
      'WCAG-004': ['color-contrast', 'color-contrast-enhanced'],
      'WCAG-007': ['focus-order-semantics', 'focusable-content'],
      'WCAG-008': ['label', 'label-title-only', 'form-field-multiple-labels'],
      'WCAG-024': ['html-has-lang', 'html-lang-valid'],
      'WCAG-005': ['keyboard', 'focusable-content'],
      'WCAG-006': ['tabindex', 'focus-order-semantics'],
      'WCAG-009': ['button-name', 'link-name', 'input-button-name'],
      'WCAG-003': ['list', 'listitem', 'definition-list'],
    };
  }

  /**
   * Apply utility enhancements to check result
   */
  private async applyUtilityEnhancements(
    baseResult: WcagCheckResult,
    page: Page,
    ruleId: string,
    scanId: string,
    utilityConfig?: Partial<UtilityIntegrationConfig>,
  ): Promise<WcagCheckResult> {
    try {
      logger.debug(`🔧 [${scanId}] Applying utility enhancements for ${ruleId}`);

      // Execute utility analysis
      const utilityAnalysis = await this.utilityManager.executeUtilityAnalysis(
        page,
        ruleId,
        scanId,
        utilityConfig,
      );

      // Enhance the result with utility insights
      const enhancement = this.utilityManager.enhanceCheckResult(
        baseResult as unknown as Record<string, unknown>,
        utilityAnalysis,
        ruleId,
      );

      // Create enhanced evidence
      const enhancedEvidence = [
        ...baseResult.evidence,
        ...enhancement.additionalEvidence.map((evidence) => ({
          type: 'info' as const,
          description: `Utility analysis: ${evidence.type}`,
          value:
            ((evidence.data as Record<string, unknown>)?.value as string) ||
            `Enhanced analysis result: ${evidence.type}`,
          selector: ((evidence.data as Record<string, unknown>)?.selector as string) || 'html',
        })),
      ];

      // Merge recommendations
      const enhancedRecommendations = [
        ...baseResult.recommendations,
        ...enhancement.utilityRecommendations.slice(0, 3), // Limit to 3 additional recommendations
        ...enhancement.frameworkSpecificGuidance.slice(0, 2), // Limit to 2 framework recommendations
      ];

      return {
        ...baseResult,
        score: enhancement.enhancedScore,
        evidence: enhancedEvidence,
        recommendations: enhancedRecommendations,
        // Add utility metadata to the result
        ...(utilityAnalysis.utilitiesUsed.length > 0 && {
          utilityEnhancement: {
            utilitiesUsed: utilityAnalysis.utilitiesUsed,
            confidence: utilityAnalysis.utilityConfidence,
            enhancedAccuracy: utilityAnalysis.enhancedAccuracy,
            executionTime: utilityAnalysis.executionTime,
            originalScore: enhancement.originalScore,
            enhancedScore: enhancement.enhancedScore,
            confidenceBoost: enhancement.confidenceBoost,
          },
        }),
      };
    } catch (error) {
      logger.warn(`⚠️ [${scanId}] Utility enhancement failed for ${ruleId}, using base result:`, {
        error: error instanceof Error ? error.message : String(error),
      });
      return baseResult;
    }
  }

  /**
   * Map axe-core impact levels to severity levels
   */
  private mapAxeImpactToSeverity(impact: string): 'error' | 'warning' | 'info' {
    switch (impact) {
      case 'critical':
      case 'serious':
        return 'error';
      case 'moderate':
        return 'warning';
      case 'minor':
      default:
        return 'info';
    }
  }

  /**
   * Validate that the page is still in a usable state
   */
  private async validatePageState(page: Page): Promise<void> {
    try {
      // Check if page is closed
      if (page.isClosed()) {
        throw new Error('Page has been closed');
      }

      // Check if page is still responsive
      await page.evaluate(() => document.readyState);

      // Check if we can access the document
      const title = await page.title().catch(() => null);
      if (title === null) {
        throw new Error('Page is not accessible - may be detached');
      }

      logger.debug('Page state validation passed');
    } catch (error) {
      logger.error('Page state validation failed:', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error(`Page is in invalid state: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
