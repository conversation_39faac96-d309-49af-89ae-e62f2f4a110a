{"data": [{"type": "interaction", "description": "Error prevention analysis", "value": "Found 3 forms/inputs with inadequate error prevention mechanisms", "elementCount": 1, "affectedSelectors": ["Found", "forms", "inputs", "with", "inadequate", "error", "prevention", "mechanisms"], "severity": "error", "metadata": {"scanDuration": 31, "elementsAnalyzed": 2, "checkSpecificData": {"automationRate": 0.85, "checkType": "enhanced-error-prevention-analysis", "comprehensiveFormAnalysis": true, "allInputValidation": true, "advancedErrorDetection": true, "preventionMethodValidation": true, "evidenceIndex": 0, "ruleId": "WCAG-066", "ruleName": "Error Prevention Enhanced", "timestamp": "2025-07-11T10:48:59.814Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "interaction", "description": "Form lacks error prevention (low risk)", "value": "2 fields, missing: validation", "selector": "#[object HTMLInputElement]", "severity": "info", "metadata": {"scanDuration": 31, "elementsAnalyzed": 2, "checkSpecificData": {"automationRate": 0.85, "checkType": "enhanced-error-prevention-analysis", "comprehensiveFormAnalysis": true, "allInputValidation": true, "advancedErrorDetection": true, "preventionMethodValidation": true, "evidenceIndex": 1, "ruleId": "WCAG-066", "ruleName": "Error Prevention Enhanced", "timestamp": "2025-07-11T10:48:59.815Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 1, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 2, "affectedSelectors": ["#[object HTMLInputElement]", "fields", "missing", "validation"]}], "timestamp": 1752230939815, "hash": "635127474362ee46bf606a641285bbe4", "accessCount": 1, "lastAccessed": 1752230939815, "size": 1684}