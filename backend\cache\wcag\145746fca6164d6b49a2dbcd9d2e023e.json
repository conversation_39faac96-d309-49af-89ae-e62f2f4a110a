{"data": [{"type": "text", "description": "Non-text content analysis summary", "value": "70/86 elements pass automated checks, 46 require manual review", "severity": "warning", "elementCount": 1, "affectedSelectors": ["elements", "pass", "automated", "checks", "require", "manual", "review"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 0, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TigerConnect-Clinical-Healthcare-Communications-Logo\" - accuracy verification needed", "selector": "img.custom-logo", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.custom-logo", "Alt", "text", "present", "TigerConnect-Clinical-Healthcare-Communications-Logo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 1, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.452Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kb-img.wp-image-513", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-513", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 2, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-541", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-541", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 3, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-1075", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1075", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 4, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-542", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-542", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 5, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kb-img.wp-image-18947", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-18947", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 6, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-541", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-541", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 7, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-1075", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1075", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 8, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-542", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-542", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 9, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kb-img.wp-image-1088", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-1088", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 10, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-541", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-541", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 11, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-1075", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1075", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 12, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-542", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-542", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 13, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.452Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kb-img.wp-image-1089", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-1089", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 14, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-541", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-541", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 15, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-1075", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1075", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 16, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-542", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-542", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 17, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TigerConnect-Clinical-Healthcare-Communications-Logo\" - accuracy verification needed", "selector": "img.custom-logo", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.custom-logo", "Alt", "text", "present", "TigerConnect-Clinical-Healthcare-Communications-Logo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 18, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Ambulance white\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-12053", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-12053", "Alt", "text", "present", "Ambulance", "white", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 19, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"group message white\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-9231", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-9231", "Alt", "text", "present", "group", "message", "white", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 20, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"group 94\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-9234", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-9234", "Alt", "text", "present", "group", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 21, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Alarm Mgt 1w\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-15420", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-15420", "Alt", "text", "present", "Alarm", "Mgt", "w", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 22, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"group 93\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-9233", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-9233", "Alt", "text", "present", "group", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 23, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"CareConduit logo\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-12200.kt-info-svg-image", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-12200.kt-info-svg-image", "Alt", "text", "present", "CareConduit", "logo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 24, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Critical Response\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-1271", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1271", "Alt", "text", "present", "Workflow", "Critical", "Response", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 25, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Emergency Department\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-1272", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1272", "Alt", "text", "present", "Workflow", "Emergency", "Department", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 26, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Inpatient Care\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-1273", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1273", "Alt", "text", "present", "Workflow", "Inpatient", "Care", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 27, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Operating Room\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-1274", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1274", "Alt", "text", "present", "Workflow", "Operating", "Room", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 28, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Post Acute Ambulatory\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-1275", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1275", "Alt", "text", "present", "Workflow", "Post", "Acute", "Ambulatory", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 29, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"blog header why healthcare needs purpose built collaboration solutions\" - accuracy verification needed", "selector": "img.kadence-video-poster.wp-image-6605", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kadence-video-poster.wp-image-6605", "Alt", "text", "present", "blog", "header", "why", "healthcare", "needs", "purpose", "built", "collaboration", "solutions", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 30, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo CapHealth G\" - accuracy verification needed", "selector": "img.wp-image-702.skip-lazy", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.wp-image-702.skip-lazy", "Alt", "text", "present", "Logo", "CapHealth", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 31, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo CommonSpirit G\" - accuracy verification needed", "selector": "img.wp-image-703.skip-lazy", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.wp-image-703.skip-lazy", "Alt", "text", "present", "Logo", "CommonSpirit", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 32, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo Geisinger G\" - accuracy verification needed", "selector": "img.wp-image-704.skip-lazy", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.wp-image-704.skip-lazy", "Alt", "text", "present", "Logo", "<PERSON><PERSON><PERSON><PERSON>", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 33, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo Innovation G\" - accuracy verification needed", "selector": "img.wp-image-705.skip-lazy", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.wp-image-705.skip-lazy", "Alt", "text", "present", "Logo", "Innovation", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 34, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo UMMC G\" - accuracy verification needed", "selector": "img.wp-image-708.skip-lazy", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.wp-image-708.skip-lazy", "Alt", "text", "present", "Logo", "UMMC", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 35, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"whclgoo\" - accuracy verification needed", "selector": "img.wp-image-709.skip-lazy", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.wp-image-709.skip-lazy", "Alt", "text", "present", "whclgoo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 36, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Doctors with tablet\" - accuracy verification needed", "selector": "img.kb-img.wp-image-21766", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-21766", "Alt", "text", "present", "Doctors", "with", "tablet", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 37, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Homepage SS 2\" - accuracy verification needed", "selector": "img.kb-img.wp-image-21735", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-21735", "Alt", "text", "present", "Homepage", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 38, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Physician Scheduling Product SS\" - accuracy verification needed", "selector": "img.kb-img.wp-image-21759", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-21759", "Alt", "text", "present", "Physician", "Scheduling", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 39, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.453Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Clinical Collaboration Product SS\" - accuracy verification needed", "selector": "img.kb-img.wp-image-21758", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-21758", "Alt", "text", "present", "Clinical", "Collaboration", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 40, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"AMEN Product SS\" - accuracy verification needed", "selector": "img.kb-img.wp-image-21791", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-21791", "Alt", "text", "present", "AMEN", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 41, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"PE Product SS\" - accuracy verification needed", "selector": "img.kb-img.wp-image-21790", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-21790", "Alt", "text", "present", "PE", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 42, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Users Physicians\" - accuracy verification needed", "selector": "img.kb-img.wp-image-1138", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-1138", "Alt", "text", "present", "Users", "Physicians", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 43, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Users IT\" - accuracy verification needed", "selector": "img.kb-img.wp-image-1136", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-1136", "Alt", "text", "present", "Users", "IT", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 44, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Users Nurse1\" - accuracy verification needed", "selector": "img.kb-img.wp-image-1137", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-1137", "Alt", "text", "present", "Users", "Nurse1", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 45, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"ICO\" - accuracy verification needed", "selector": "img.kb-img.wp-image-1148", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-1148", "Alt", "text", "present", "ICO", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 46, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"2024 best in klas recognition\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-20332", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-20332", "Alt", "text", "present", "best", "in", "klas", "recognition", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 47, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"g2 logo png\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-20321", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-20321", "Alt", "text", "present", "g2", "logo", "png", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 48, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"<PERSON><PERSON>ner logo blue small digital\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-20906", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-20906", "Alt", "text", "present", "<PERSON><PERSON><PERSON>", "logo", "blue", "small", "digital", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 49, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"RSC 04\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-1388", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1388", "Alt", "text", "present", "RSC", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 50, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Webinar Preview Transform Pre Hospital and Transfer Communication\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-21616", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-21616", "Alt", "text", "present", "Webinar", "Preview", "Transform", "Pre", "Hospital", "and", "Transfer", "Communication", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 51, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"resources inforgraphic\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-5852", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-5852", "Alt", "text", "present", "resources", "inforgraphic", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 52, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Case Study Tufts Medical Center Preview\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-20048", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-20048", "Alt", "text", "present", "Case", "Study", "Tufts", "Medical", "Center", "Preview", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 53, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"RSC 02\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-1386", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1386", "Alt", "text", "present", "RSC", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 54, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Blog Expanding Care Beyong Hospital Walls Preview\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-21615", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-21615", "Alt", "text", "present", "Blog", "Expanding", "Care", "<PERSON><PERSON>", "Hospital", "Walls", "Preview", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 55, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Beckers 2024 report state of healthcare collaboration how communication inefficiencies impact clinician productivity 11.08.57 AM 1\" - accuracy verification needed", "selector": "img.kb-img.wp-image-17384", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-17384", "Alt", "text", "present", "<PERSON><PERSON>", "report", "state", "of", "healthcare", "collaboration", "how", "communication", "inefficiencies", "impact", "clinician", "productivity", "AM", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 56, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Alt text too short to be descriptive", "selector": "img.kb-img.wp-image-1370", "severity": "error", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-1370", "Alt", "text", "too", "short", "to", "be", "descriptive"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 57, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TigerConnect-Clinical-Healthcare-Communications-Logo-R\" - accuracy verification needed", "selector": "img.kb-img.wp-image-47", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-47", "Alt", "text", "present", "TigerConnect-Clinical-Healthcare-Communications-Logo-R", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 58, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.454Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Google-Play-App-Clinical-Collaboration-Apple-App-TigerConnect\" - accuracy verification needed", "selector": "img.kb-img.wp-image-40", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-40", "Alt", "text", "present", "Google-Play-App-Clinical-Collaboration-Apple-App-TigerConnect", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 133, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 59, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.455Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752230680455, "hash": "ca623f8d75ae052e64a65b42524b9cca", "accessCount": 1, "lastAccessed": 1752230680455, "size": 53461}